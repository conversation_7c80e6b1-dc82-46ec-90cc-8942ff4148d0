<template>
  <div class="flex flex-col items-center justify-center h-750">
    <van-empty :image="image" :description="description">
      <slot />
      <div v-if="showLogin" >
        <div class="text-sm text-gray-500 mb-4">{{ showLoginText }}</div>
        <div
          class="mt-2 flex items-center justify-center rounded-full bg-theme-blue px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 active:bg-theme-blue-600"
          @click="handleLogin"
        >
          <i class="i-mdi-login mr-1.5" />
          去登录
        </div>
      </div>
    </van-empty>
  </div>
</template>

<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      image: 'error' | 'network' | 'search' | 'default';
      description: string;
      showLogin?: boolean;
      showLoginText?: string;
    }>(),
    {
      image: 'error',
      description: '',
      showLogin: false,
      showLoginText: '登录后可查看',
    },
  );

  const emit = defineEmits<{
    (e: 'login'): void;
  }>();

  const handleLogin = () => {
    emit('login');
  };
</script>
