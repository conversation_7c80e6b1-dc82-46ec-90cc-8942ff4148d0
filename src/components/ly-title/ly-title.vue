<template>
  <div class="flex items-center justify-between">
    <!-- 标题部分 -->
    <div class="relative font-bold" :class="props.fontSize">
      <div class="relative tracking-wide" :class="titleVariantClass">
        <text>{{ props.title }}</text>
        <!-- 装饰性下划线 -->
        <text class="absolute -bottom-0 left-0 w-8 h-0.5 bg-gradient-to-r rounded-full shadow-sm" :class="underlineVariantClass"></text>
        <!-- 次要装饰点 -->
        <text class="absolute -bottom-0 left-10 w-2 h-0.5 bg-blue-300/60 rounded-full"></text>
      </div>
    </div>

    <!-- 更多按钮 -->
    <div v-if="props.more" class="group flex items-center active:bg-blue-50/80 active:scale-95 transition-all duration-200" @click="emit('more')">
      <text class="text-xs text-gray-400 group-active:text-blue-600 transition-colors duration-200">
        {{ props.more }}
      </text>
      <text class="i-mdi-chevron-right -mr-1 text-gray-400 group-active:text-blue-500 group-active:translate-x-0.5 transition-all duration-200 text-[18px]"></text>
    </div>
  </div>
</template>
<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      title: string;
      more?: string;
      fontSize?: string;
      variant?: 'default' | 'primary' | 'secondary';
    }>(),
    {
      fontSize: 'text-base',
      variant: 'default',
    },
  );

  const emit = defineEmits<{
    (e: 'more'): void;
  }>();

  // 根据变体计算样式类
  const titleVariantClass = computed(() => {
    switch (props.variant) {
      case 'primary':
        return 'text-blue-800';
      case 'secondary':
        return 'text-gray-700';
      default:
        return 'text-gray-800';
    }
  });

  const underlineVariantClass = computed(() => {
    switch (props.variant) {
      case 'primary':
        return 'from-blue-600 to-blue-500';
      case 'secondary':
        return 'from-gray-500 to-gray-400';
      default:
        return 'from-blue-500 to-blue-400';
    }
  });
</script>
