<template>
  <ly-layout>
    <!-- 表单内容区域 -->
    <view class="p-4">
      <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top" :border="false">
        <!-- 内容编辑卡片 -->
        <view class="bg-white rounded-lg shadow-md pb-2 p-4 mb-4">
          <uni-forms-item name="fbnr" label="内容编辑" required>
            <uni-easyinput v-model="formData.fbnr" type="textarea" placeholder="分享你的想法..." :maxlength="500" :auto-height="true"  :input-border="false" />
            <view class="flex justify-between items-center mt-2">
              <text class="text-xs text-gray-400">请合理发布社区内容</text>
              <text class="text-xs text-gray-400">{{ formData.fbnr.length }}/500</text>
            </view>
          </uni-forms-item>
        </view>
        <!-- 风险词提示 -->
        <view v-if="riskWords.length > 0" class="bg-orange-50 rounded-lg p-3 mb-4 border border-orange-200">
          <view class="flex items-center justify-between mb-2">
            <view class="flex items-center">
              <text class="i-mdi-alert text-orange-500 text-base mr-2"></text>
              <text class="text-orange-700 text-sm font-medium">检测到风险词汇</text>
            </view>
            <text @tap="clearRiskWords" class="i-mdi-close text-orange-400 text-base"></text>
          </view>

          <view class="flex flex-wrap gap-2">
            <view v-for="(word, index) in riskWords" :key="index" class="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs">
             {{ word.RiskWords }}
            </view>
          </view>
        </view>

        <!-- 检查图片风险 -->
        <view v-if="riskImages.length > 0 && formData.fbtp.length > 0" class="bg-red-50 rounded-lg p-3 mb-4 border border-red-200">
          <view class="flex items-center justify-between mb-2">
            <view class="flex items-center">
              <text class="i-mdi-image-off text-red-500 text-base mr-2"></text>
              <text class="text-red-700 text-sm font-medium">检测到风险图片</text>
            </view>
            <text @tap="clearRiskImages" class="i-mdi-close text-red-400 text-base"></text>
          </view>

          <view class="flex flex-wrap gap-2">
            <view v-for="(image, index) in riskImages" :key="index" class="bg-red-100 text-red-700 px-2 py-1 rounded text-xs">
              {{ image.Description }}: 第{{ image.index + 1 }}张图片
            </view>
          </view>
        </view>

        <!-- 图片上传卡片 -->
        <view class="bg-white rounded-lg shadow-md pb-2 p-4 mb-4">
          <uni-forms-item name="fbtp" label="添加图片" required>
            <ly-upload v-model="formData.fbtp" title="最多上传9张图片" :limit="9" :return-upload-results="true">
              <div class="flex items-center justify-center flex-col gap-2">
                <text class="i-mdi-camera-enhance-outline text-2xl text-theme-blue"></text>
                <text class="text-sm text-gray-400">上传图片</text>
              </div>
            </ly-upload>
          </uni-forms-item>
        </view>
      </uni-forms>
      <ly-line-bar  />
    </view>
    <!-- 底部固定按钮 -->
    <ly-fixed-btns :buttons="buttons" />
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { ImageTypes } from '@/monkey/types';
  import type { CommonTypes } from '@/monkey/types';

  const { user: userInfo } = storeToRefs(monkey.$stores.useUserStore());

  const formRef = ref(); // 表单引用
  const riskWords = ref<string[]>([]); // 风险词
  const riskImages = ref<string[]>([]); // 图片是否合规

  // 表单数据
  const formData = reactive({
    fbnr: '',
    fbrbh: userInfo.value?.id,
    fbr: userInfo.value?.zsxm || userInfo.value?.nc,
    fbtp: [] as ImageTypes.UploadResult[],
    shzt: '审核中',
    shzt_key: 'shz',
    dzs: 0,
    pls: 0,
  });

  const buttons = [
    {
      text: '发布社区',
      icon: 'i-mdi-send',
      round: 'rounded-full',
      click: () => handlePublish(),
    },
  ];

  // 表单验证规则
  const rules: FormRules = {
    fbnr: {
      rules: [
        { required: true, errorMessage: '请输入内容' },
        { minLength: 5, errorMessage: '内容至少5个字符' },
        { maxLength: 500, errorMessage: '内容不能超过500个字符' },
      ],
    },
    fbtp: {
      rules: [{ required: true, errorMessage: '请上传图片' }],
    },
  };

  // 计算是否可以发布
  const canPublish = computed(() => {
    return formData.fbnr.trim().length >= 5 && (formData.fbnr.length > 0 || formData.fbtp.length > 0);
  });

  // 清除风险词提示
  const clearRiskWords = () => {
    riskWords.value = [];
  };

  // 清除风险图片提示
  const clearRiskImages = () => {
    riskImages.value = [];
  };

  // 发布社区
  const handlePublish = async () => {
    if (!formRef.value) return;

    try {
      console.log('🚀 ~ handlePublish ~ formData:', formData);
      await formRef.value.validate();

      // 检查发布内容是否合规
      const checkContentResult = await monkey.$api.auth.checkPublishContent(formData.fbnr);

      // 检查是否有风险词汇
      if (checkContentResult.errcode === 200 && checkContentResult.data?.data) {
        const { RiskLevel, Result } = checkContentResult.data.data;
        if (['high', 'medium'].includes(RiskLevel)) {
          riskWords.value = Result;
          return;
        }
      }

      // 检查图片是否合规
      const images = formData.fbtp.map((item) => item.refId);
      const checkImageResult = await monkey.$api.auth.checkPublishImage(images);
      let isImageRisk = false;
      if (checkImageResult.errcode === 200 && checkImageResult.data) {
        const data = checkImageResult.data;
        // data 返回的数对象多个对象
        for (const key in data) {
          console.log('🚀 ~ handlePublish ~ key:', key);
          // 获取第几章图片
          const index = images.findIndex((item) => item === key);
          const item = data[key];
          if (['high', 'medium'].includes(item.RiskLevel)) {
            // 如果图片有风险，则提示风险词
            riskImages.value = item.Result.map((item) => {
              return {
                ...item,
                index,
              };
            });
            isImageRisk = true;
            return;
          }
        }
      }

      if (isImageRisk) return;

      // 获取审批对象
      const approval = monkey.$helper.param.getAuthineFormSubmitApproval({
        activityCode: 'Activity2',
      });

      // 获取业务对象
      const bizObject = monkey.$helper.param.getAuthineFormSubmitBizObject({
        data: formData,
        schemaCode: 'ltxxb',
      });

      // 获取提交参数
      const params =  monkey.$helper.param.getAuthineFormSubmitParams(
        {
          workflowCode: 'ltlc',
        },
        bizObject,
        approval,
      );
      const publishResult = await monkey.$api.authine.submitAuthineForm(params);
      if (publishResult.errcode == 0) {
        uni.$emit('IMAGE:PUBLISH', true);
        monkey.$helper.toast.success('发布成功，请等待审核');
        // 返回上一页或跳转到社区列表
        monkey.$router.navigateBack(1, { delay: 1000 });
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 监听内容变化，清除风险词提示
  watch(
    () => formData.fbnr,
    () => {
      if (riskWords.value.length > 0) {
        riskWords.value = [];
      }
    },
  );
</script>

<style scoped></style>
