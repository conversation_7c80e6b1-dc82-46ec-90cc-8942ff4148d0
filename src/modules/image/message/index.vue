<template>
  <ly-layout>
    <!-- 社区信息列表 -->
    <scroll-view
      class="p-4 size-full box-border"
      :scroll-y="true"
      refresher-enabled="true"
      refresher-default-style="none"
      :refresher-triggered="lazyload.triggered"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRestore"
      @scrolltolower="onScrollTolower"
    >
      <!-- 骨架屏加载状态 -->
      <ly-skeleton-screen :is-loading="lazyload.isLoading && list.length === 0" :count="3" />
      <!-- 加载中 -->
      <ly-loading v-if="lazyload.isLoading" />
      <!-- 社区信息卡片列表 -->
      <div v-else-if="list.length > 0" class="space-y-4">
        <ui-image-card v-for="post in list" :key="post.id" :post="post" :isShowAction="false" />
      </div>
      <!-- 空状态 -->
      <ly-empty v-if="lazyload.isEmpty" description="暂无社区内容" :image="monkey.$url.cdn(monkey.$config.empty.noMessage)" />
      <ly-load-more v-if="list.length > 0" :status="lazyload.status" />
      <ly-line-bar />
    </scroll-view>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { ImageTypes } from '@/monkey/types';

  const { user: userInfo } = storeToRefs(monkey.$stores.useUserStore());

  // 响应式数据
  const list = ref<ImageTypes.ImagePost[]>([]);

  // 懒加载配置
  const lazyload = reactive({
    page: 0,
    size: 4,
    isFlag: false,
    status: 'more',
    isEmpty: false,
    isLoading: false,
    pageCount: 0,
    triggered: false,
    _freshing: false,
  });

  /**
   * 获取帖子列表
   */
  const getList = async (callback?) => {
    const queryCondition = [
      {
        ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'fbrbh', 0, userInfo.value?.id),
      },
    ];
    const params = monkey.$helper.param.getAuthineListParams({
      schemaCode: 'ltxxb',
      queryCondition: queryCondition,
    });
    const { errcode, data } = await monkey.$api.authine.getAuthineList(params);
    if (errcode === 0 && data?.content) {
      const newList = data.content.map((item) => ({ ...item.data })) as ImageTypes.ImagePost[];
      list.value = [...list.value, ...newList];
      lazyload.pageCount = data.totalElements;
      lazyload.isEmpty = !list.value.length;
      callback && callback();
    }
  };

  /**
   * 初始化函数，用于重置状态和数据
   */
  const init = () => {
    lazyload.isFlag = false;
    list.value = [];
    lazyload.page = 0;
  };

  // 下拉刷新的逻辑
  const onRefresh = () => {
    if (lazyload._freshing) return;
    lazyload._freshing = true;
    lazyload.isLoading = true;
    if (!lazyload.triggered) lazyload.triggered = true;
    init();
    setTimeout(() => {
      getList(() => {
        lazyload.triggered = false;
        lazyload._freshing = false;
        lazyload.isLoading = false;
      });
    }, 600);
  };

  // 上拉加载更多的逻辑
  const onScrollTolower = () => {
    lazyload.isFlag = true;
    if (list.value.length >= lazyload.pageCount) return (lazyload.status = 'no-more');
    lazyload.status = 'loading';
    lazyload.page++;
    getList();
  };

  // 用于恢复加载状态的函数
  const onRestore = () => {
    lazyload.triggered = 'restore';
  };

  // 页面加载
  onLoad(async () => {
    getList();
  });
</script>
