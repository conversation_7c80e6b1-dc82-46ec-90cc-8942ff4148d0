<template>
  <view class="flex flex-col size-full">
    <!-- 已选产品信息展示区域 -->
    <div v-if="confirmedGoods.length === 0" class="flex justify-center items-center bg-white rounded-lg">
      <van-empty :image="image" description="暂无产品">
        <div class="text-sm text-gray-500 mb-4">请选择您要入库的产品</div>
        <div
          class="flex items-center justify-center rounded-full tracking-widest bg-theme-blue px-6 py-2.5 text-sm font-medium text-white shadow-md transition-all duration-300 hover:shadow-lg active:scale-95 active:bg-theme-blue-600"
          @click="handleSelectGoods"
        >
          <i class="i-mdi-package-variant-closed mr-1.5" />
          选择产品
        </div>
      </van-empty>
    </div>

    <!-- 显示选中产品信息可输入数量和重量 -->
    <div v-else class="flex flex-col size-full">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between flex-shrink-0">
        <div class="flex items-center">
          <i class="i-mdi-package-variant mr-2 text-theme-blue" />
          <text class="text-base font-medium text-gray-800">已选产品</text>
          <div class="text-sm text-gray-500 ml-2">
            共 <text class="text-theme-blue">{{ confirmedGoods.length }}</text> 件
          </div>
        </div>
        <div class="flex items-center text-sm text-theme-blue" @click="handleSelectGoods">
          <i class="i-mdi-plus mr-1" />
          添加产品
        </div>
      </div>

      <!-- 可滚动的产品列表 -->
      <scroll-view scroll-y class="absolute top-5 left-0 right-0 bottom-0 p-4 box-border">
        <div class="space-y-2 pb-4">
          <div v-for="(item, index) in confirmedGoods" :key="item.id" class="bg-white rounded-lg p-3 shadow-sm border border-gray-100">
            <!-- 产品基本信息 -->
            <div class="flex items-center justify-between mb-2">
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-800 truncate">{{ item.bzmc }}</div>
                <div class="text-xs text-gray-500 mt-0.5 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
              </div>
              <div @click="handleRemoveGood(index)" class="w-6 h-6 flex items-center justify-center rounded-full bg-red-50 text-red-500 active:bg-red-100 transition-colors ml-2 flex-shrink-0">
                <i class="i-mdi-close text-xs" />
              </div>
            </div>

            <!-- 产品规格输入 -->
            <div class="mb-2 flex flex-col gap-1">
              <label class="text-xs font-medium text-gray-700">产品规格</label>
              <uni-easyinput v-model="item.cpgg" :placeholder="`请输入${item.bzmc}规格`" @change="handleSpecChange(index)" @clear="handleClearSpec(index)" />
              <div class="text-xs text-gray-500">如：统、个子、片等</div>
            </div>

            <!-- 数量和重量输入 -->
            <div class="grid grid-cols-2 gap-2">
              <!-- 数量输入 -->
              <div class="flex flex-col gap-1">
                <label class="text-xs font-medium text-gray-700">数量</label>
                <van-stepper v-model="item.rksl" :min="1" :max="999" :step="1" integer @change="handleQuantityChange(index, $event)" class="w-full" button-size="35px" input-width="70px" />
                <div class="text-xs text-gray-500">单位：件</div>
              </div>

              <!-- 重量输入 -->
              <div class="flex flex-col gap-1">
                <label class="text-xs font-medium text-gray-700">总重量</label>
                <uni-easyinput v-model="item.rkzl" :placeholder="`请输入${item.bzmc}重量`" type="number" @change="handleValidateWeight(index)" @clear="handleClearWeight(index)" />
                <div class="text-xs text-gray-500">单位：KG</div>
              </div>
            </div>
          </div>
        </div>
      </scroll-view>
    </div>
    <ui-popup v-model:show="showGoodsSelector" title="选择产品" position="bottom" :safe-bottom="false">
      <view class="h-[85vh] flex flex-col bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="px-4 bg-white">
          <uni-search-bar v-model="goodsSearchValue" placeholder="请输入产品名称或拼音" @confirm="handleGoodsSearch" @clear="handleGoodsClear" cancelButton="none" />
        </div>
        <div class="flex-1 overflow-hidden">
          <scroll-view scroll-y class="h-full" v-if="goodsListLetter.length > 0">
            <div class="px-4 pt-2 box-border">
              <van-index-bar :index-list="goodsIndexList" :sticky="false">
                <view v-for="item in goodsListLetter" :key="item.letter">
                  <text class="text-sm font-medium text-gray-800">{{ item.letter }}</text>
                  <div class="grid grid-cols-4 gap-3 my-2">
                    <div
                      v-for="item in item.data"
                      :key="item.id"
                      @click="handleGoodsItemClick(item)"
                      class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col relative transition-all duration-200"
                      :class="{ 'ring-2 ring-theme-blue bg-blue-50': isSelected(item.id) }"
                    >
                      <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
                      <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
                      <!-- 选中状态指示器 -->
                      <div v-if="isSelected(item.id)" class="absolute -top-1 -right-1 w-5 h-5 bg-theme-blue rounded-full flex items-center justify-center">
                        <i class="i-mdi-check text-white text-xs" />
                      </div>
                    </div>
                  </div>
                </view>
              </van-index-bar>
            </div>
          </scroll-view>
        </div>
        <!-- 底部确认区域 -->
        <div class="px-4 py-3 bg-white border-t border-gray-100 shadow-lg rounded-t-lg" :class="`pb-[env(safe-area-inset-bottom)]`">
          <!-- 已选产品统计 -->
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center">
              <i class="i-mdi-package-variant mr-2 text-theme-blue" />
              <text class="text-sm font-medium text-gray-700">已选产品</text>
            </div>
            <div class="flex items-center">
              <text class="text-lg font-bold text-theme-blue">{{ selectedGoods.length }}</text>
              <text class="text-sm text-gray-500 ml-1">件</text>
            </div>
          </div>

          <!-- 已选产品列表预览 -->
          <div v-if="selectedGoods.length > 0" class="mb-3">
            <div class="flex flex-wrap gap-1 max-h-16 overflow-hidden">
              <div v-for="item in selectedGoods.slice(0, 6)" :key="item.id" class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                {{ item.bzmc }}
              </div>
              <div v-if="selectedGoods.length > 6" class="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">+{{ selectedGoods.length - 6 }}</div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <ly-footer-btns className="flex gap-3">
            <div
              @click="handleClearSelection"
              class="flex-1 py-2.5 px-4 flex items-center justify-center gap-2 tracking-widest bg-gray-100 text-gray-600 rounded-lg text-sm transition-all duration-200 active:bg-gray-200 active:scale-95"
              :disabled="selectedGoods.length === 0"
              :class="{ 'opacity-50 cursor-not-allowed': selectedGoods.length === 0 }"
            >
              <i class="i-mdi-close-circle" />
              <text>清空</text>
            </div>
            <div
              @click="handleConfirmSelection"
              class="flex-1 py-2.5 px-4 flex items-center justify-center gap-2 tracking-widest bg-gradient-to-r from-theme-blue to-blue-500 text-white rounded-lg text-sm shadow-md transition-all duration-200 active:shadow-lg active:scale-95"
              :disabled="selectedGoods.length === 0"
              :class="{ 'opacity-50 cursor-not-allowed': selectedGoods.length === 0 }"
            >
              <i class="i-mdi-check-circle" />
              <text>确认选择</text>
            </div>
          </ly-footer-btns>
        </div>
      </view>
    </ui-popup>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicineTypes } from '@/monkey/types';

  const image = ref(monkey.$url.cdn(monkey.$config.empty.noContent)); // 空状态图片
  const showGoodsSelector = ref(false); // 是否显示产品选择器
  const goodsList = ref<MedicineTypes.MedicinalItem[]>([]); // 药材列表
  const goodsListLetter = ref<MedicineTypes.MedicinalItem[]>([]); // 索引列表
  const goodsIndexList = ref<string[]>([]); // 索引列表
  const goodsSearchValue = ref<string>(''); // 搜索值
  const selectedGoods = ref<MedicineTypes.MedicinalItem[]>([]); // 确认的产品列表（包含数量和重量信息）
  const confirmedGoods = ref<MedicineTypes.MedicinalItem[]>([]);
  const scrollTop = ref(0); // 滚动位置控制

  // 定义触发事件
  const emit = defineEmits<{
    (e: 'itemClick', item: MedicineTypes.MedicinalItem): void;
    (e: 'confirm', items: MedicineTypes.MedicinalItem[]): void;
    (e: 'update', items: MedicineTypes.MedicinalItem[]): void;
  }>();

  /**
   * 检查产品是否已选中
   * @param id 产品ID
   */
  const isSelected = (id: string | number) => {
    return selectedGoods.value.some((item) => item.id === id);
  };

  /**
   * 处理药材项点击事件（多选模式）
   * @param item 药材项
   */
  const handleGoodsItemClick = (item: MedicineTypes.MedicinalItem) => {
    const index = selectedGoods.value.findIndex((selected) => selected.id === item.id);

    if (index > -1) {
      // 如果已选中，则取消选中
      selectedGoods.value.splice(index, 1);
    } else {
      // 如果未选中，则添加到选中列表
      selectedGoods.value.push(item);
    }

    // 保持原有的单个点击事件
    emit('itemClick', item);
  };

  /**
   * 清空选择
   */
  const handleClearSelection = () => {
    selectedGoods.value = [];
  };

  /**
   * 确认选择
   */
  const handleConfirmSelection = () => {
    if (selectedGoods.value.length > 0) {
      // 将选中的产品转换为扩展类型，添加默认数量和重量
      const newGoods: MedicineTypes.MedicinalItem[] = selectedGoods.value.map((item) => ({
        rkzl: 1,
        rkyc: item.bzmc,
        rksl: 1,
        rowStatus: 'Added',
        bzmc: item.bzmc,
        pinyin: item.pinyin,
        pinyinInitials: item.pinyinInitials,
        goodsId: item.id,
        rkje: item.je,
        id: '',
        cpgg: '',
      }));

      // 合并到确认产品列表，避免重复
      newGoods.forEach((newItem) => {
        const existingIndex = confirmedGoods.value.findIndex((item) => item.goodsId === newItem.goodsId);
        if (existingIndex === -1) {
          confirmedGoods.value.push(newItem);
        }
      });

      // 触发更新事件
      emit('update', [...confirmedGoods.value]);

      showGoodsSelector.value = false;
      // 清空选择状态
      // selectedGoods.value = [];
    } else {
      monkey.$helper.toast.warning('请选择产品');
    }
  };

  /**
   * 移除产品
   * @param index 产品索引
   */
  const handleRemoveGood = (index: number) => {
    confirmedGoods.value.splice(index, 1);
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 处理数量变化
   * @param index 产品索引
   * @param value 新的数量值
   */
  const handleQuantityChange = (index: number, value: number) => {
    confirmedGoods.value[index].rksl = value?.detail;
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 验证重量输入
   * @param index 产品索引
   */
  const handleValidateWeight = (index: number) => {
    const item = confirmedGoods.value[index];
    if (item.rkzl < 0 || isNaN(item.rkzl)) {
      item.rkzl = 0;
    }
    // 保留一位小数
    item.rkzl = Math.round(item.rkzl * 10) / 10;
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 清空重量
   * @param index 产品索引
   */
  const handleClearWeight = (index: number) => {
    confirmedGoods.value[index].rkzl = 1;
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 处理产品规格变化
   * @param index 产品索引
   */
  const handleSpecChange = (index: number) => {
    confirmedGoods.value[index].cpgg = confirmedGoods.value[index].cpgg?.trim();
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 清空产品规格
   * @param index 产品索引
   */
  const handleClearSpec = (index: number) => {
    confirmedGoods.value[index].cpgg = '';
    emit('update', [...confirmedGoods.value]);
  };

  /**
   * 从ywsc字段中提取拼音并生成首字母缩写
   * @param ywsc ywsc字段内容，格式如：鸡内金,jnj,鸡黄皮,鸡盹皮
   * @returns 拼音信息对象
   */
  const extractPinyinFromYwsc = (ywsc: string) => {
    if (!ywsc) return { original: '', initials: '' };

    const ywscParts = ywsc.split(',');

    // 尝试找到拼音缩写（通常是纯小写字母的部分）
    for (const part of ywscParts) {
      const trimmedPart = part.trim();
      // 检查是否是拼音缩写（纯小写字母）
      if (/^[a-z]+$/.test(trimmedPart)) {
        // 简单的首字母提取：按2个字符一组提取首字母
        let initials = '';
        for (let i = 0; i < trimmedPart.length; i += 2) {
          if (i < trimmedPart.length) {
            initials += trimmedPart.charAt(i);
          }
        }

        return {
          original: trimmedPart,
          initials: initials,
        };
      }
    }

    return { original: '', initials: '' };
  };

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        // 将数据转换为MedicinalItem类型，并提取拼音字段
        const enrichedData = data.map((item: MedicineTypes.MedicinalItem) => {
          const pinyinInfo = extractPinyinFromYwsc(item.ywsc);
          return {
            ...item,
            pinyin: pinyinInfo.original, // 原始拼音字符串
            pinyinInitials: pinyinInfo.initials, // 拼音首字母缩写
          };
        });

        goodsList.value = enrichedData;
        // 将数据转换为索引列表
        goodsListLetter.value = createLetterList(enrichedData);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicineTypes.MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 使用已提取的拼音或产品名称的首字母
      const firstLetter = (item.pinyin && item.pinyin.charAt(0).toUpperCase()) || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组
      groupedData[firstLetter].push(item);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    goodsIndexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterGoodsList = (list: MedicineTypes.MedicinalItem[]) => {
    const searchTerm = goodsSearchValue.value.toLowerCase().trim();

    if (!searchTerm) {
      return list;
    }

    return list.filter((item) => {
      // 匹配产品名称
      const nameMatch = item.bzmc.toLowerCase().includes(searchTerm);

      // 匹配完整拼音
      const fullPinyinMatch = item.pinyin && item.pinyin.toLowerCase().includes(searchTerm);

      // 匹配拼音首字母缩写
      const initialsMatch = item.pinyinInitials && item.pinyinInitials.toLowerCase().includes(searchTerm);

      // 拼音字符分解匹配：将拼音分解成单个字符，然后检查搜索词是否能在拼音中连续匹配
      let pinyinCharMatch = false;
      if (item.pinyin && searchTerm.length > 1) {
        const pinyinLower = item.pinyin.toLowerCase();
        // 检查搜索词的每个字符是否能在拼音中按顺序找到
        let pinyinIndex = 0;
        let searchIndex = 0;

        while (pinyinIndex < pinyinLower.length && searchIndex < searchTerm.length) {
          if (pinyinLower[pinyinIndex] === searchTerm[searchIndex]) {
            searchIndex++;
          }
          pinyinIndex++;
        }

        // 如果搜索词的所有字符都能按顺序在拼音中找到
        pinyinCharMatch = searchIndex === searchTerm.length;
      }

      return nameMatch || fullPinyinMatch || initialsMatch || pinyinCharMatch;
    });
  };

  /**
   * 重置滚动位置到顶部
   */
  const resetScrollPosition = () => {
    scrollTop.value = 0;
    // 强制触发滚动更新
    nextTick(() => {
      scrollTop.value = 1;
      nextTick(() => {
        scrollTop.value = 0;
      });
    });
  };

  /**
   * 搜索
   */
  const handleGoodsSearch = () => {
    const result = filterGoodsList(goodsList.value);
    if (result.length > 0) {
      // 过滤列表
      goodsListLetter.value = createLetterList(result);
      // 重置滚动位置
      resetScrollPosition();
    } else monkey.$helper.toast.warning('暂无数据');
  };

  /**
   * 清空搜索
   */
  const handleGoodsClear = () => {
    goodsListLetter.value = createLetterList(goodsList.value);
    // 重置滚动位置
    resetScrollPosition();
  };

  // 处理选择产品事件
  const handleSelectGoods = () => {
    showGoodsSelector.value = true;
  };

  onMounted(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped></style>
