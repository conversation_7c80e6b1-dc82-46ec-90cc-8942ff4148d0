<template>
  <ly-layout class="h-screen">
    <!-- Tab切换 -->
    <div class="px-2 py-1.5 bg-white">
      <van-tabs :active="activeTab" @change="handleTabChange" color="#3e8bf7">
        <van-tab v-for="tab in tabs" :key="tab.code" :title="tab.name" :name="tab.code"> </van-tab>
      </van-tabs>
    </div>
    <!-- 列表 -->
    <scroll-view
      class="absolute top-12 bottom-0 p-4 box-border flex flex-col items-center w-full"
      :scroll-y="true"
      refresher-enabled="true"
      refresher-default-style="none"
      :refresher-triggered="lazyload.triggered"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRestore"
      @scrolltolower="onScrollTolower"
    >
      <!-- 骨架屏加载状态 -->
      <ly-skeleton-screen :is-loading="lazyload.isLoading && list.length === 0" :count="3" />
      <!-- 加载中 -->
      <ly-loading v-if="lazyload.isLoading" />
      <!-- 列表 -->
      <div v-for="(record, index) in list" :key="record.id" class="bg-white rounded-lg shadow-md border border-gray-100 p-4 mb-4">
        <!-- 记录卡片内容 -->
        <div class="flex flex-col">
          <!-- 头部信息 -->
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-400">{{ formatDate(record?.createdTime) }}</div>
            <div :class="getStatusClass(record?.sftg_key)" class="inline-flex px-3 py-1 text-xs font-medium rounded-full">
              {{ record?.sftg }}
            </div>
          </div>

          <!-- 预约信息 -->
          <div class="grid grid-cols-3 gap-4 mt-4">
            <div>
              <div class="text-gray-400 text-xs mb-1">入库日期</div>
              <div class="font-medium text-gray-700">{{ monkey.$dayjs(record?.yyrq).format('YYYY-MM-DD') }}</div>
            </div>
            <div>
              <div class="text-gray-400 text-xs mb-1">时间段</div>
              <div class="font-medium text-gray-700">{{ record?.yysjd }}</div>
            </div>
            <div>
              <div class="text-gray-400 text-xs mb-1">预约人</div>
              <div class="font-medium text-gray-700">{{ record?.yyr }}</div>
            </div>
          </div>

          <!-- 产品信息 -->
          <div class="border-t border-gray-50 mt-4" v-if="record?.yymx.length > 0">
            <div class="text-xs text-gray-400 my-3">入库产品</div>
            <!-- 单个产品展示 -->
            <div v-if="record?.yymx.length === 1" class="bg-gray-50 rounded-lg p-3">
              <div class="font-medium text-sm text-gray-800 mb-1">{{ record?.yymx[0].rkyc }}</div>
              <div class="text-xs text-gray-500">数量: {{ record?.yymx[0].rksl }}（件） | 总重量: {{ record?.yymx[0].rkzl }}（KG）</div>
            </div>
            <!-- 多个产品展示 -->
            <div v-else class="bg-gray-50 rounded-lg p-3">
              <div class="text-sm text-gray-800 mb-2">
                {{
                  record?.yymx
                    .slice(0, 4)
                    .map((p) => p.rkyc)
                    .join('、')
                }}{{ record?.yymx.length > 3 ? '等' : '' }}
              </div>
              <div class="text-xs text-gray-500">共{{ record?.yymx.length }}种产品 | 总数量: {{ getTotalQuantity(record) }}（件） | 总重量: {{ getTotalWeight(record) }}（KG）</div>
            </div>
          </div>

          <!-- 底部操作按钮 -->
          <div class="flex items-center justify-end gap-2 mt-4 pt-4 border-t border-dotted border-gray-200">
            <div
              v-if="record?.sftg_key?.includes('shz')"
              @click.stop="handleDelete(record, index)"
              class="flex gap-1 items-center justify-center px-3 py-1.5 text-sm bg-red-50 text-red-600 rounded-full border border-red-200 hover:bg-red-100 transition-colors"
            >
              <i class="i-mdi-delete-outline text-xs" />
              删除订单
            </div>
            <div @click.stop="handleViewRecord(record)" class="flex gap-1 items-center justify-center px-3 py-1.5 text-sm bg-blue-50 text-blue-600 rounded-full border border-blue-200 hover:bg-blue-100 transition-colors">
              <i class="i-mdi-eye text-xs" />
              查看详情
            </div>
            <div
              v-if="record?.sftg_key?.includes('wfk')"
              @click.stop="payOrder(record)"
              class="flex gap-1 items-center justify-center px-3 py-1.5 text-sm bg-green-50 text-green-600 rounded-full border border-green-200 hover:bg-green-100 transition-colors"
            >
              <i class="i-mdi-cash-multiple text-xs" />
              立即支付
            </div>
          </div>
        </div>
      </div>
      <ly-empty v-if="lazyload.isEmpty" description="暂无入库订单" :image="monkey.$url.cdn(monkey.$config.empty.noOrder)" />
      <ly-load-more v-if="list.length > 0" :status="lazyload.status" />
    </scroll-view>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { UserTypes } from '@/monkey/types';

  const { user: userInfo } = storeToRefs(monkey.$stores.useUserStore());

  const activeTab = ref<string>('all'); // 当前选中的tab的code
  const activeTabName = ref<string>(''); // 当前选中的tab的index
  const activeTabTitle = ref<string>(''); // 所有tab
  const tabs = ref<{ code: string; name: string }[]>([]); // 列表数据
  const list = ref<UserTypes.UserReservationInboundForm[]>([]); // 列表数据

  // 懒加载配置
  const lazyload = reactive({
    page: 0,
    size: 4,
    isFlag: false, // 数据状态
    status: 'more',
    isEmpty: false, // 数据图片
    isLoading: false,
    pageCount: 0, // 总数
    triggered: false, // 监听刷新状态
    _freshing: false, // 刷新监听
  });

  // 状态相关方法
  const getStatusClass = (statusCode: string) => {
    const statusClasses = {
      shz: 'bg-blue-50 text-blue-600', // 审核中 - 蓝色
      shtg: 'bg-green-50 text-green-600', // 已通过 - 绿色
      shwtg: 'bg-red-50 text-red-600', // 未通过 - 红色
      wfk: 'bg-amber-50 text-amber-600', // 未付款 - 橙色
      yfk: 'bg-green-50 text-green-600', // 已付款 - 绿色
    };
    return statusClasses[statusCode as keyof typeof statusClasses] || 'bg-gray-50 text-gray-600';
  };

  // 日期格式化
  const formatDate = (dateString: string) => {
    return dateString ? monkey.$dayjs(dateString).format('YYYY-MM-DD dddd A HH:mm') : '-';
  };

  // 查看记录详情
  const handleViewRecord = (record: UserTypes.UserReservationInboundForm) => {
    // 监听订单详情页的取消订单事件
    uni.$once('ORDER_DETAIL', (e) => {
      if (e.action == 'delete') {
        deleteOrder(e.id);
      }
    });
    monkey.$router.navigateTo('/modules/reservation/inbound/record/detail?id=' + record.id);
  };

  /**
   * 删除订单
   * @param id 订单id
   */
  const deleteOrder = (id: string) => {
    const idx = list.value.findIndex((item) => item.id === id);
    if (idx !== -1) {
      list.value.splice(idx, 1);
    }
  };

  /**
   * 删除订单
   */
  const handleDelete = async (record: UserTypes.UserReservationInboundForm, index: number) => {
    const modal = await monkey.$helper.toast.modal({
      title: '提示',
      content: '确定删除该订单吗？',
      confirmText: '确定',
      cancelText: '取消',
    });
    if (!modal) return;
    const { errcode, data, errmsg } = await monkey.$api.authine.deleteAuthineData(
      monkey.$helper.param.getAuthineFormDeleteParams({
        ids: [record.id],
        schemaCode: 'yyrkd',
      }),
    );
    if (errcode === 0 && data) {
      monkey.$helper.toast.success(errmsg);
      deleteOrder(record.id);
    }
  };

  /**
   * 支付订单
   */
  const payOrder = async (record: UserTypes.UserReservationInboundForm) => {
    monkey.$helper.toast.success('暂未开放');
  };

  /**
   * 切换tab
   */
  const handleTabChange = (e) => {
    activeTabTitle.value = e.detail.title;
    activeTabName.value = e.detail.name;
    init();
    getList();
  };

  /**
   * 获取总数量
   */
  const getTotalQuantity = (record: UserTypes.UserReservationInboundForm) => {
    return record?.yymx.reduce((acc, item) => acc + item.rksl, 0);
  };

  /**
   * 获取总重量
   */
  const getTotalWeight = (record: UserTypes.UserReservationInboundForm) => {
    return record?.yymx.reduce((acc, item) => acc + item.rkzl, 0);
  };

  /**
   * 获取入库状态
   */
  const getInboundStatus = async () => {
    const { errcode, data } = await monkey.$api.authine.getEnableRecordsByDictionaryId(monkey.$config.dict.inboundStatus);
    if (errcode === 0 && data) {
      tabs.value = [...data];
    }
  };

  /**
   * 获取入库列表
   */
  const getList = async (callback?: () => void) => {
    const queryCondition = [
      {
        ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'yyrbh', 0, userInfo.value?.id),
      },
    ];
    // 如果选中的tab不是全部，则添加查询条件
    if (activeTabName.value !== 'all' && activeTabName.value !== '') {
      queryCondition.push({
        ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'sftg', 14, `[{"key":"${activeTabName.value}","value":"${activeTabTitle.value}"}]`),
      });
    }
    const params = monkey.$helper.param.getAuthineListParams({
      schemaCode: 'yyrkd',
      queryCondition: queryCondition,
      page: lazyload.page,
      size: lazyload.size,
    });
    const { errcode, data } = await monkey.$api.authine.getAuthineListIsAuth(params);
    if (errcode === 0 && data?.content) {
      const newList = data.content.map((item) => ({ ...item.data })) as UserTypes.UserReservationInboundForm[];
      list.value = [...list.value, ...newList];
      lazyload.pageCount = data.totalElements; // 更新总页数
      lazyload.isEmpty = !list.value.length; // 更新数据状态
      callback && callback();
    } else {
      lazyload.isEmpty = true;
    }
  };

  /**
   * 初始化函数，用于重置状态和数据
   * @returns {void}
   */
  const init = () => {
    lazyload.isFlag = false; // 重置加载标志
    list.value = []; // 重置课程列表
    lazyload.page = 0; // 重置页码为第1页
  };

  // 下拉刷新的逻辑
  const onRefresh = () => {
    if (lazyload._freshing) return; // 如果正在刷新，则不执行
    lazyload._freshing = true; // 设置为正在刷新
    lazyload.isLoading = true;
    if (!lazyload.triggered) lazyload.triggered = true; // 初始时设置为已触发
    init(); // 初始化数据和状态
    setTimeout(() => {
      getList(() => {
        lazyload.triggered = false; // 重置触发状态
        lazyload._freshing = false; // 重置刷新状态
        lazyload.isLoading = false; // 重置加载状态
      });
    }, 600);
  };

  // 上拉加载更多的逻辑
  const onScrollTolower = () => {
    lazyload.isFlag = true; // 开启加载状态
    if (list.value.length >= lazyload.pageCount) return (lazyload.status = 'no-more');
    lazyload.status = 'loading'; // 设置加载状态为加载中
    lazyload.page++; // 加载下一页
    getList(); // 获取课程列表
  };

  // 用于恢复加载状态的函数 设置触发状态为恢复
  const onRestore = () => {
    lazyload.triggered = 'restore';
  };

  const getResults = () => {
    getInboundStatus();
    init();
    getList();
  };

  onLoad(() => {
    // 监听登录成功事件
    uni.$on('LOGIN_SUCCESS', (e: boolean) => {
      if (e) getResults();
    });
    getResults();
  });

  onUnload(() => {
    uni.$off('LOGIN_SUCCESS');
  });
</script>

<style scoped>
  /* 自定义样式可以在这里添加 */
</style>
