<template>
  <ly-layout className="h-screen">
    <div class="px-2 py-4 bg-white">
      <uni-steps :options="steps" :active="active" />
    </div>
    <div class="flex-1 pt-4 box-border">
      <swiper class="size-full" :current="active" :disable-touch="true">
        <!-- 预约时间 -->
        <swiper-item class="size-full" @touchmove.stop>
          <view class="p-4 bg-white mb-4">
            <div class="mb-4 flex items-center justify-between">
              <div class="mb-4">
                <uni-section title="选择预约时间" type="line" padding="0"></uni-section>
              </div>
              <div v-if="selectedDate" class="text-sm text-gray-500">{{ selectedDate }} {{ selectedTimeSlot ? `(${selectedTimeSlot})` : '' }}</div>
            </div>
            <!-- 日期选择横向滚动 -->
            <scroll-view scroll-x>
              <Dates :cols="5" @selectDate="handleSelectDate" @more="handleMoreDates" :selectedDateIndex="selectedDateIndex" type="ck" typeName="出库" />
            </scroll-view>
          </view>
          <!-- 显示时间段上午 -->
          <view class="p-4 bg-white mb-4">
            <div class="mb-4">
              <uni-section title="上午 选择时间段" type="line" padding="0"></uni-section>
            </div>
            <view>
              <Times :times="amTimes" type="am" :selectedIndex="selectedTimeAMIndex" @selectTime="handleSelectTimeAM" />
            </view>
          </view>
          <!-- 显示时间段下午 -->
          <view class="p-4 bg-white">
            <div class="mb-4">
              <uni-section title="下午 选择时间段" type="line" padding="0"></uni-section>
            </div>
            <view>
              <Times :times="pmTimes" type="pm" :selectedIndex="selectedTimePMIndex" @selectTime="handleSelectTimePM" />
            </view>
          </view>
        </swiper-item>
        <!-- 产品选择 -->
        <swiper-item class="size-full" @touchmove.stop>
          <view class="px-4 size-full">
            <Goods @update="handleConfirmGoods" />
          </view>
        </swiper-item>
        <!-- 预约信息 -->
        <swiper-item class="size-full" @touchmove.stop>
          <view class="px-4 size-full">
            <Info ref="infoRef" />
          </view>
        </swiper-item>
        <!-- 订单确认 -->
        <swiper-item class="size-full" @touchmove.stop>
          <view class="px-4 size-full">
            <Order :order="orderData" />
          </view>
        </swiper-item>
      </swiper>
    </div>
    <ly-fixed-btns :fixed="false" :buttons="nextButton" v-if="active === 0"></ly-fixed-btns>
    <ly-fixed-btns :fixed="false" :buttons="goodsButton" v-if="active === 1"> </ly-fixed-btns>
    <ly-fixed-btns :fixed="false" :buttons="infoButton" v-if="active === 2"></ly-fixed-btns>
    <ly-fixed-btns :fixed="false" :buttons="confirmButton" v-if="active === 3"></ly-fixed-btns>
    <ui-popup v-model:show="showMoreDates" title="选择预约时间" position="bottom">
      <div class="p-4 box-border">
        <Dates :isMore="false" :cols="4" gap="3" :selectedDateIndex="selectedDateIndex" @selectDate="handleSelectDate" type="ck" typeName="出库" />
      </div>
    </ui-popup>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import Dates from '../components/dates.vue';
  import Times from '../components/times.vue';
  import Info from '../components/info.vue';

  import Goods from './components/goods.vue';
  import Order from './components/order.vue';
  import { HomeTypes, MedicineTypes, CommonTypes, UserTypes } from '@/monkey/types';

  const { user: userInfo, hasLogin } = storeToRefs(monkey.$stores.useUserStore());

  const active = ref(0);

  // 选中的日期索引
  const selectedDateIndex = ref(0);

  // 选中的日期
  const selectedDate = ref<string | null>(null);

  // 选中的时间段
  const selectedTimeSlot = ref<string | null>('');

  // 选中的时间段索引
  const selectedTimePMIndex = ref(null);

  // 选中的时间段索引
  const selectedTimeAMIndex = ref(null);

  // 选中的时间段列表
  const selectTimesList = ref<any[]>([]);

  // 显示更多日期弹窗
  const showMoreDates = ref<boolean>(false);

  // 选中的产品列表
  const selectedGoodsList = ref<MedicineTypes.MedicinalItem[]>([]);

  // 新增：预约信息表单
  const infoRef = ref<any>(null);

  // 订单数据计算属性
  const orderData = computed(() => {
    const formData = infoRef.value?.getFormData() || {};
    return {
      yyrq: selectedDate.value,
      yysjd: selectedTimeSlot.value,
      yymx: selectedGoodsList.value,
      kssjd: selectedTimeSlot.value.split('-')[0],
      jssjd: selectedTimeSlot.value.split('-')[1],
      rksjzhubid: selectedDateId.value,
      rkzsjzhibid: selectedTimeId.value,
      sftg: '审核中',
      sftg_key: 'shz',
      ddbh: monkey.$helper.utils.generateRandomString(32),
      ...formData,
    } as UserTypes.UserReservationInboundForm;
  });

  // 步骤条
  const steps = ref([{ title: '预约时间' }, { title: '出库产品' }, { title: '信息补充' }, { title: '订单确认' }]);

  // 下一步按钮
  const nextButton = ref<CommonTypes.ActionItem[]>([{ text: '下一步', icon: 'i-mdi-arrow-right', type: 'primary', click: () => handleSubmit(1) }]);

  // 产品选择按钮
  const goodsButton = ref<CommonTypes.ActionItem[]>([
    { text: '上一步', icon: 'i-mdi-arrow-left', type: 'secondary', click: () => handleSubmit(0) },
    { text: '确认产品', icon: 'i-mdi-check', type: 'primary', click: () => handleSubmit(2) },
  ]);

  // 预约信息按钮
  const infoButton = ref<CommonTypes.ActionItem[]>([
    { text: '上一步', icon: 'i-mdi-arrow-left', type: 'secondary', click: () => handleSubmit(1) },
    { text: '确认信息', icon: 'i-mdi-check', type: 'primary', click: () => handleSubmit(3) },
  ]);

  // 订单确认按钮
  const confirmButton = ref<CommonTypes.ActionItem[]>([
    { text: '上一步', icon: 'i-mdi-arrow-left', type: 'secondary', click: () => handleSubmit(2) },
    { text: '提交订单', icon: 'i-mdi-check-circle', type: 'primary', click: () => handleSubmit(4) },
  ]);

  // 当前日期列表
  const currentDates = ref<HomeTypes.InboundDateItem[]>([]);

  // 上午时间段
  const amTimes = ref<HomeTypes.InboundTimeItem[]>([]);

  // 下午时间段
  const pmTimes = ref<HomeTypes.InboundTimeItem[]>([]);

  // 选中时间ID
  const selectedDateId = ref<string>('');

  // 选中时间ID
  const selectedTimeId = ref<string>('');

  // 选中时间
  const selectedTime = ref<string>('');

  /**
   * 处理日期选择
   * @param date 选中的日期
   * @param index 日期索引
   */
  const handleSelectDate = async (date: HomeTypes.InboundDateItem, index: number) => {
    if (!date?.id) {
      return monkey.$helper.toast.error('无效的日期');
    }

    // 重置时间选择状态
    selectedTimeAMIndex.value = null;
    selectedTimePMIndex.value = null;
    selectedDateIndex.value = index;
    selectedTimeSlot.value = '';

    // 设置选中日期
    selectedDate.value = date.dateStr;
    showMoreDates.value = false;
    selectedDateId.value = date.id;

    // 获取该日期的时间段数据
    await getTimes(date.id, date.dateStr);
  };

  // 处理更多日期按钮点击
  const handleMoreDates = () => {
    // 打开日历选择器
    showMoreDates.value = true;
  };

  /**
   * 选择上午时间段
   * @param item 时间段
   * @param index 时间段索引
   */
  const handleSelectTimeAM = (item, index) => {
    console.log('🚀 ~ handleSelectTimeAM ~ item:', item);
    selectedTimePMIndex.value = null;
    selectedTimeAMIndex.value = index;
    selectedTimeSlot.value = item.time;
    selectedTimeId.value = item.id;
  };

  /**
   * 选择下午时间段
   * @param item 时间段
   * @param index 时间段索引
   */
  const handleSelectTimePM = (item, index) => {
    console.log('🚀 ~ handleSelectTimePM ~ item:', item);
    selectedTimeAMIndex.value = null;
    selectedTimePMIndex.value = index;
    selectedTimeSlot.value = item.time;
    selectedTimeId.value = item.id;
  };

  const handleConfirmGoods = (goods: MedicineTypes.MedicinalItem[]) => {
    console.log('🚀 ~ handleConfirmGoods ~ goods:', goods);
    selectedGoodsList.value = [...goods];
  };

  /**
   * 获取日期的时间段数据
   * @param id 日期ID
   * @param dateStr 日期字符串
   */
  const getTimes = async (id: string, dateStr: string) => {
    try {
      // 显示加载提示
      monkey.$helper.toast.loading('获取时间段数据...');

      // 获取日期详细信息
      const res = await monkey.$api.authine.getAuthineForm({
        schemaCode: 'crkyysj',
        objectId: id,
      });

      if (res.errcode !== 0 || !res.data?.bizObject?.data) return monkey.$helper.toast.error('获取日期信息失败');

      const data = res.data.bizObject.data;

      // 验证日期状态
      if (data.sfqy_key !== 'qy') return monkey.$helper.toast.error('该日期已禁用');

      const today = monkey.$dayjs();
      const selectedDateObj = monkey.$dayjs(dateStr);

      if (selectedDateObj.isBefore(today, 'day')) return monkey.$helper.toast.error('该日期已过期');

      // 处理时间段数据的通用函数
      const mapTimeSlots = (timeSlots: any[]): HomeTypes.InboundTimeItem[] => {
        return timeSlots.map((item) => ({
          time: monkey.$dayjs(`${dateStr} ${item.kssj}`).format('HH:mm') + '-' + monkey.$dayjs(`${dateStr} ${item.jssj}`).format('HH:mm'),
          count: Math.max(0, item.zdyys - item.yys), // 确保数量不为负数
          id: item.id,
          date: dateStr,
          isDisabled: item.sfqy_key !== 'qy',
          isExpired: monkey.$dayjs(`${dateStr} ${item.jssj}`).isBefore(today),
        }));
      };

      // 设置上午和下午时间段
      amTimes.value = data.amzb ? mapTimeSlots(data.amzb) : [];
      pmTimes.value = data.pmzb ? mapTimeSlots(data.pmzb) : [];

      console.log('🚀 ~ getTimes ~ 选中日期:', dateStr, '时间段数据:', {
        上午: amTimes.value,
        下午: pmTimes.value,
      });

      // 关闭加载提示
      monkey.$helper.toast.hideLoading();
    } catch (error) {
      console.error('getTimes error:', error);
      monkey.$helper.toast.error('获取日期信息失败，请重试');
    }
  };

  const handleSubmit = async (index) => {
    // 预约时间
    if (index === 1) {
      if (userInfo.value.sfrz == 0) {
        const moadl = await monkey.$helper.toast.modal({
          title: '提示',
          content: '请您先进行身份认证，否则无法进行入库预约',
          confirmText: '去认证',
        });
        if (moadl) return monkey.$router.navigateTo('/modules/user/auth/person/index');
        else return;
      }

      // 如果未选择日期，则打开日期选择弹窗
      console.log('🚀 ~ handleSubmit ~ selectedDate.value:', selectedDate.value);
      if (!selectedDate.value) return monkey.$helper.toast.error('请选择预约日期');

      // 如果未选择时间段，则打开时间段选择弹窗
      if (!selectedTimeSlot.value) return monkey.$helper.toast.error('请选择预约时间段');

      // 选中产品
    } else if (index === 2) {
      // 如果未选择产品，则打开产品选择弹窗
      console.log('🚀 ~ handleSubmit ~ selectedGoods:', selectedGoodsList.value);
      if (selectedGoodsList.value.length === 0) return monkey.$helper.toast.error('请选择入库产品');

      // 需判断 如果产品的斤数是0 则提示 请输入斤数 那个产品
      for (const item of selectedGoodsList.value) {
        if (item.weight === 0) return monkey.$helper.toast.error(`请输入${item.bzmc}的斤数`);
      }
    } else if (index === 3) {
      const validate = await infoRef.value.validate();
      if (!validate) return;
      const info = infoRef.value.getFormData();
      console.log('🚀 ~ handleSubmit ~ info:', info);
    } else if (index === 4) {
      // 提交订单
      await handleSubmitInbound();
      return;
    }

    active.value = index;

    // 如果未选择产品，则打开产品选择弹窗

    console.log('🚀 ~ handleNextStep ~ active.value:', active.value);
  };

  const handleSubmitInbound = async () => {
    try {
      // 显示加载提示
      monkey.$helper.toast.loading('正在提交订单...');

      // 构建订单数据
      const orderInfo = orderData.value;

      console.log('🚀 ~ 提交入库订单 ~ orderInfo:', orderInfo);

      // 获取审批对象
      const approval = monkey.$helper.param.getAuthineFormSubmitApproval({
        activityCode: 'Activity2',
      });

      // 获取业务对象
      const bizObject = monkey.$helper.param.getAuthineFormSubmitBizObject({
        data: orderInfo,
        schemaCode: 'yyrkd',
      });

      // 获取提交参数
      const params =  monkey.$helper.param.getAuthineFormSubmitParams(
        {
          workflowCode: 'rkyy',
        },
        bizObject,
        approval,
      );

      // 如果获取提交参数失败，则提示
      if (!params) {
        monkey.$helper.toast.error('获取提交参数失败');
        return;
      }

      console.log('🚀 ~ handleSubmitInbound ~ params:', params);

      // 这里应该调用API提交订单
      const result = await monkey.$api.authine.submitAuthineForm(params);
      if (result.errcode === 0) {
        monkey.$helper.toast.success('订单提交成功，请等待审核');
        monkey.$router.switchTab('/pages/index/index', { delay: 1000 });
      }
    } catch (error) {
      console.error('提交订单失败:', error);
      monkey.$helper.toast.error('提交订单失败，请重试');
    }
  };

  onLoad(() => {
    console.log('onLoad');
  });
</script>

<style lang="scss" scoped></style>
