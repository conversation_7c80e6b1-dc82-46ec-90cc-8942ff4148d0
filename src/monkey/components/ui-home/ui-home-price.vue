<template>
  <div class="rounded-lg bg-white shadow-md px-4 py-4 relative">
    <slot name="title"></slot>
    <div class="flex items-center gap-2 absolute top-[19px] right-20">
      <view
        class="flex items-center cursor-pointer px-2 py-0.5 rounded-full transition-all duration-200 text-xs"
        :class="activeButton === 0 ? 'bg-blue-500 text-white' : 'bg-gray-100 text-blue-500 active:bg-gray-200'"
        @click="switchToZhoData"
      >
        <text class="i-mdi-arrow-up-bold mr-1"></text>
        <text>周涨跌</text>
      </view>
      <view
        class="flex items-center cursor-pointer px-2 py-0.5 rounded-full transition-all duration-200 text-xs"
        :class="activeButton === 1 ? 'bg-blue-500 text-white' : 'bg-gray-100 text-blue-500 active:bg-gray-200'"
        @click="switchToYueData"
      >
        <text class="i-mdi-arrow-up-bold mr-1"></text>
        <text>月涨跌</text>
      </view>
    </div>
    <div class="w-full flex flex-col items-center">
      <div class="grid grid-cols-4 gap-4 pb-4 mb-4 border-b border-gray-100 w-full">
        <div class="text-xs font-medium text-gray-600 text-center">品名/规格</div>
        <div class="text-xs font-medium text-gray-600 text-center">市场</div>
        <div class="text-xs font-medium text-gray-600 text-center">价格</div>
        <div class="text-xs font-medium text-gray-600 text-center">涨跌幅</div>
      </div>
      <swiper class="w-full h-64" :autoplay="true" :duration="3000" :interval="1500" :circular="true" :vertical="true" :display-multiple-items="6">
        <swiper-item class="w-full" v-for="(item, index) in list" :key="index" @click="handleClick(item)">
          <div class="grid grid-cols-4 gap-2 items-center rounded-lg active:bg-gray-50/70 transition-colors ">
            <div class="text-center flex flex-col items-center gap-0.5">
              <div class="truncate text-xs text-gray-800">{{ item.name }}</div>
              <div class="truncate text-xs text-gray-400">{{ item.origin }}</div>
            </div>
            <div class="text-xs text-gray-600 text-center truncate">{{ item.market }}</div>
            <div class="text-xs font-bold text-center text-green-600">{{ item.price }}</div>
            <!-- <div class="text-xs font-bold text-center" :class="{ 'text-green-600': isPositiveOrZero(item.change), 'text-red-600': !isPositiveOrZero(item.change) }">{{ item.price }}</div> -->
            <div class="flex justify-center">
              <span
                class="inline-block px-3 py-1 rounded-full text-xs font-medium transition-transform active:scale-105"
                :class="{ 'text-red-600 bg-red-50': isPositive(item.change), 'text-green-600 bg-green-50': isNegative(item.change), 'text-gray-500 bg-gray-50': isZero(item.change) }"
              >
                {{ item.change }}
              </span>
            </div>
          </div>
        </swiper-item>
      </swiper>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { ref } from 'vue';
  import monkey from '@/monkey';
  import type { MedicineTypes } from '@/monkey/types';

  import zhoData from './zho.js';
  import yueData from './yue.js';

  // 定义药材行情数据类型
  interface MedicinalMarketItem {
    name: string;
    origin: string;
    market: string;
    price: number;
    change: string;
    changeValue: number;
  }

  // 定义当前显示的数据列表
  const list = ref<MedicinalMarketItem[]>(zhoData);

  // 定义当前选中的按钮（0=周涨跌, 1=月涨跌）
  const activeButton = ref(0);

  // 切换到周数据
  const switchToZhoData = () => {
    // 打乱一下数组
    list.value = zhoData;
    activeButton.value = 0;
  };

  // 切换到月数据
  const switchToYueData = () => {
    list.value = yueData;
    activeButton.value = 1;
  };

  // const props = defineProps<{
  //   list: MedicineTypes.MedicinalCurrentItemDetail[];
  // }>();

  /**
   * 点击药材行情
   * @param item 药材行情
   */
  const handleClick = (item: MedicinalMarketItem) => {
    // monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.glyc}`);
  };

  // 判断涨跌幅是否为正数、负数或零
  const isPositive = (change: string) => change.startsWith('↑') && change !== '↑0.00%';
  const isNegative = (change: string) => change.startsWith('↓') && change !== '↓0.00%';
  const isZero = (change: string) => change === '↑0.00%' || change === '↓0.00%' || change === '0.00%';
  const isPositiveOrZero = (change: string) => !isNegative(change);
</script>
