<template>
  <div class="bg-white rounded-lg shadow-md mb-4 overflow-hidden transition-all duration-300 active:shadow-xl active:shadow-blue-200/60">
    <!-- 用户信息头部 -->
    <div class="flex items-center justify-between p-4 pb-3">
      <div class="flex items-center space-x-3">
        <!-- 用户头像 -->
        <div class="relative">
          <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 p-0.5 shadow-lg">
            <div class="w-full h-full rounded-full bg-white flex items-center justify-center overflow-hidden">
              <image v-if="post?.fbrbh?.yhtx && post?.fbrbh?.yhtx?.length > 0" :src="monkey.$url.cdn(post?.fbrbh?.yhtx[0].refId)" class="w-full h-full object-cover rounded-full" mode="aspectFill" />
              <div v-else class="i-mdi-account text-2xl text-gray-400"></div>
            </div>
          </div>
          <!-- 认证徽章 -->
          <div v-if="post?.fbrbh?.sfrz === 1" class="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
            <div class="i-mdi-check text-white text-xs"></div>
          </div>
        </div>

        <!-- 用户信息 -->
        <div class="flex-1">
          <div class="flex items-center space-x-2">
            <div class="font-semibold text-gray-900 text-sm">{{ post.fbr || '匿名用户' }}</div>
            <div v-if="post?.fbrbh?.sfrz === 1" class="px-2 py-0.5 bg-gradient-to-r from-green-400 to-green-500 text-white text-xs rounded-full font-medium">已认证</div>
          </div>
          <div class="flex items-center space-x-2 mt-1">
            <text class="text-xs text-gray-500">{{ monkey.$dayjs(post.modifiedTime).fromNow() }}</text>
            <!-- <text v-if="post.location" class="text-xs text-gray-400 flex items-center">
              <div class="i-mdi-map-marker text-xs mr-1"></div>
              {{ post.location }}
            </text> -->
          </div>
        </div>
      </div>

      <!-- 更多操作 -->
      <div class="p-2 rounded-full active:bg-gray-50 transition-colors cursor-pointer" @click="showMoreActions">
        <div class="i-mdi-dots-horizontal text-gray-400 text-lg"></div>
      </div>
    </div>

    <!-- 内容文字 -->
    <div v-if="post.fbnr" class="px-4 pb-3">
      <p class="text-gray-800 text-sm leading-relaxed" :class="{ 'line-clamp-3': !isExpanded }">
        {{ post.fbnr }}
      </p>
      <div v-if="post.fbnr.length > 100" @click="toggleExpanded" class="text-blue-500 text-xs mt-1 active:text-blue-600 transition-colors">
        {{ isExpanded ? '收起' : '展开' }}
      </div>
    </div>

    <!-- 标签 -->
    <div v-if="post.tags?.length" class="px-4 pb-3">
      <div class="flex flex-wrap gap-2">
        <text v-for="tag in post.tags" :key="tag" class="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full border border-blue-100 active:bg-blue-100 transition-colors cursor-pointer"> #{{ tag }} </text>
      </div>
    </div>

    <!-- 图片网格 -->
    <div v-if="post.fbtp?.length" class="px-4 pb-4">
      <div :class="getImageGridClass(post.fbtp.length)">
        <div v-for="(image, index) in post.fbtp.slice(0, 9)" :key="image.id" class="relative rounded-xl overflow-hidden cursor-pointer group" @click="previewImages(index)">
          <image lazy-load :src="monkey.$url.cdn(image.refId)" class="w-full h-full object-cover transition-transform duration-300 group-active:scale-105" mode="aspectFill" :lazy-load="true" />
          <!-- 更多图片提示 -->
          <div v-if="index === 8 && post.fbtp.length > 9" class="absolute inset-0 bg-black/60 flex items-center justify-center">
            <text class="text-white font-semibold text-lg">+{{ post.fbtp.length - 9 }}</text>
          </div>
          <!-- 悬浮遮罩 -->
          <div class="absolute inset-0 bg-black/0 group-active:bg-black/10 transition-colors duration-300"></div>
        </div>
      </div>
    </div>

    <!-- 互动区域 -->
    <div class="px-4 py-3 border-t border-gray-50" v-if="isShowAction">
      <div class="flex items-center justify-between">
        <!-- 左侧互动按钮 -->
        <div class="flex items-center space-x-6">
          <!-- 点赞 -->
          <div @click="toggleLike" class="flex items-center gap-2 group transition-all duration-300" :class="{ 'text-red-500': post.isLiked, 'text-gray-500': !post.isLiked }">
            <div class="relative flex items-center justify-center">
              <div :class="['text-lg transition-all duration-300', post.isLiked ? 'i-mdi-heart scale-110' : 'i-mdi-heart-outline group-active:scale-110']"></div>
              <!-- 点赞动画效果 -->
              <div v-show="likeAnimation" class="absolute inset-0 i-mdi-heart text-red-400 animate-ping"></div>
            </div>
            <text class="text-sm font-medium">{{ formatCount(post.dzs) }}</text>
          </div>

          <!-- 评论 -->
          <div @click="showComments" class="flex items-center gap-2 text-gray-500 group active:text-blue-500 transition-colors duration-300">
            <div class="i-mdi-comment-outline text-lg group-active:scale-110 transition-transform duration-300"></div>
            <text class="text-sm font-medium">{{ formatCount(post.pls) }}</text>
          </div>

          <!-- 分享 -->
          <div @click="sharePost" class="flex items-center gap-2 text-gray-500 group active:text-green-500 transition-colors duration-300">
            <div class="i-mdi-share-variant-outline text-lg group-active:scale-110 transition-transform duration-300"></div>
            <text class="text-sm font-medium">分享</text>
          </div>
        </div>

        <!-- 右侧收藏按钮 -->
        <div v-if="false" @click="toggleBookmark" class="p-2 rounded-full active:bg-gray-50 transition-colors duration-300" :class="{ 'text-yellow-500': isBookmarked, 'text-gray-400': !isBookmarked }">
          <div :class="['text-lg transition-all duration-300', isBookmarked ? 'i-mdi-bookmark scale-110' : 'i-mdi-bookmark-outline active:scale-110']"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { ImageTypes } from '@/monkey/types';
  import relativeTime from 'dayjs/plugin/relativeTime';
  monkey.$dayjs.extend(relativeTime);

  interface Props {
    post: ImageTypes.ImagePost;
    isShowAction?: boolean;
  }

  interface Emits {
    (e: 'like', postId: string): void;
    (e: 'comment', postId: string): void;
    (e: 'share', postId: string): void;
    (e: 'bookmark', postId: string): void;
    (e: 'previewImages', images: string[], current: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    isShowAction: true,
  });

  const emit = defineEmits<Emits>();

  // 响应式状态
  const isExpanded = ref(false);
  const isBookmarked = ref(false);
  const likeAnimation = ref(false);

  // 格式化时间
  const formatTime = (time: string) => {
    return monkey.$dayjs(time).fromNow();
  };

  // 格式化数量
  const formatCount = (count: number) => {
    if (count < 1000) return count.toString();
    if (count < 10000) return (count / 1000).toFixed(1) + 'k';
    return (count / 10000).toFixed(1) + 'w';
  };

  // 获取图片网格样式
  const getImageGridClass = (count: number) => {
    if (count === 1) return 'grid grid-cols-1 gap-1.5 max-w-xs h-40';
    if (count === 2) return 'grid grid-cols-2 gap-1.5 max-w-sm h-28';
    if (count === 3) return 'grid grid-cols-3 gap-1.5 max-w-md h-24';
    if (count === 4) return 'grid grid-cols-2 gap-1.5 max-w-sm h-32';
    return 'grid grid-cols-3 gap-1.5 max-w-md h-28';
  };

  // 切换展开状态
  const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value;
  };

  // 点赞操作
  const toggleLike = async () => {
    // 触发点赞动画
    if (!props.post.isLiked) {
      likeAnimation.value = true;
      setTimeout(() => {
        likeAnimation.value = false;
      }, 600);
      // 获取审批对象
      const approval = monkey.$helper.param.getAuthineFormSubmitApproval({ activityCode: '' });
      // 获取业务对象
      const bizObject = monkey.$helper.param.getAuthineFormSubmitBizObject({
        data: {
          yhbh: monkey.$stores.useUserStore().user.id,
          zxbh: props.post.id,
          zxhyh: monkey.$stores.useUserStore().user.id + ',' + monkey.$stores.useUserStore().user.name,
        },
        schemaCode: 'dzjlb',
      });
      const params = await monkey.$helper.param.getAuthineFormSubmitParams(
        {
          workflowCode: '',
          formType: '2',
        },
        bizObject,
        approval,
      );
      const likeRes = await monkey.$api.authine.submitAuthineForm(params);

      if (likeRes.errcode == 0) {
        props.post.isLiked = !props.post.isLiked;
        props.post.dzs += props.post.isLiked ? 1 : -1;

        await monkey.$api.authine.executeBizServiceTest({
          code: 'add_likes',
          serviceCode: 'xch',
          testInputParametersMap: {
            zxid: props.post.id,
          },
        });
      } else if (likeRes.errcode == 800150006) {
        props.post.isLiked = !props.post.isLiked;
        props.post.dzs += props.post.isLiked ? 1 : -1;

        await monkey.$api.authine.deleteAuthineData( monkey.$helper.param.getAuthineFormDeleteParams({
        ids: [record.id],
        schemaCode: 'yyrkd',
      }),);

        await monkey.$api.authine.executeBizServiceTest({
          code: 'minus_likes',
          serviceCode: 'xch',
          testInputParametersMap: {
            zxid: props.post.id,
          },
        });
      }
    }
    // emit('like', props.post.id);
  };

  // 显示评论
  const showComments = () => {
    emit('comment', props.post.id);
  };

  // 分享帖子
  const sharePost = () => {
    emit('share', props.post.id);
  };

  // 收藏操作
  const toggleBookmark = () => {
    isBookmarked.value = !isBookmarked.value;
    emit('bookmark', props.post.id);
  };

  // 预览图片
  const previewImages = (index: number) => {
    const imageUrls = props.post.fbtp.map((img) => monkey.$url.cdn(img.refId));
    monkey.$helper.utils.previewImage(imageUrls, index);
  };

  // 显示更多操作
  const showMoreActions = () => {
    return;
    uni.showActionSheet({
      itemList: ['举报', '不感兴趣', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            console.log('举报');
            break;
          case 1:
            console.log('不感兴趣');
            break;
          case 2:
            console.log('复制链接');
            break;
        }
      },
    });
  };
</script>

<style scoped lang="scss"></style>
