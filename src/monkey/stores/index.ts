// ===========================================
// Stores 统一导出入口
// ===========================================

import { useAuthModalStore } from './modal';
import { useUserStore } from './user';
import { useErrorsStore } from './errors';
// ===========================================
// 导出所有 stores
// ===========================================
export const stores = {
  useAuthModalStore,
  useUserStore,
  useErrorsStore,
} as const;

// ===========================================
// 默认导出 - 保持向后兼容
// ===========================================
export default stores;

// ===========================================
// 单独导出各个 store - 便于按需导入
// ===========================================
export { useAuthModalStore, useUserStore, useErrorsStore };
