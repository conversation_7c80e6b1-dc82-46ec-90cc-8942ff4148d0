import type { UserProfile } from '../types';
import monkey from '@/monkey';

interface UserState {
  user: UserProfile | null;
  token: string | null;
  hasLogin: boolean;
  sessionKey: string | null;
  authineToken: string | null;
  authineTokenExpiresIn: number | null;
}

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  user: 'user',
  token: 'token',
  hasLogin: 'hasLogin',
  sessionKey: 'sessionKey',
  authineToken: 'authineToken',
  authineTokenExpiresIn: 'authineTokenExpiresIn',
} as const;

export const useUserStore = defineStore('user', () => {
  // 从本地存储初始化状态
  const initState = (): UserState => {
    try {
      return {
        user: uni.getStorageSync(STORAGE_KEYS.user) || null,
        token: uni.getStorageSync(STORAGE_KEYS.token) || null,
        hasLogin: uni.getStorageSync(STORAGE_KEYS.hasLogin) || false,
        sessionKey: uni.getStorageSync(STORAGE_KEYS.sessionKey) || null,
        authineToken: uni.getStorageSync(STORAGE_KEYS.authineToken) || null,
        authineTokenExpiresIn: uni.getStorageSync(STORAGE_KEYS.authineTokenExpiresIn) || null,
      };
    } catch (error) {
      console.error('Failed to initialize state from storage:', error);
      return {
        user: null,
        token: null,
        hasLogin: false,
        sessionKey: null,
        authineToken: null,
        authineTokenExpiresIn: null,
      };
    }
  };

  const state = reactive<UserState>(initState());

  /**
   * 通用存储方法
   * @param key - 存储键名
   * @param value - 存储值
   */
  const setStorage = (key: StorageKey, value: any) => {
    try {
      uni.setStorageSync(STORAGE_KEYS[key], value);
    } catch (error) {
      console.error(`Failed to set storage for key: ${key}`, error);
    }
  };

  /**
   * 删除存储方法
   * @param key - 要删除的存储键名
   */
  const removeStorage = (key: StorageKey) => {
    try {
      uni.removeStorageSync(STORAGE_KEYS[key]);
    } catch (error) {
      console.error(`Failed to remove storage for key: ${key}`, error);
    }
  };

  /**
   * 设置用户信息
   */
  const setUser = (user: UserProfile): void => {
    state.user = user;
    state.hasLogin = true;
    setStorage('user', user);
    setStorage('hasLogin', true);
  };

  /**
   * 设置token
   * @param token 用户令牌
   */
  const setToken = (token: string): void => {
    state.token = token;
    setStorage('token', token);
  };

  /**
   * 设置认证Token
   * @param authineToken 认证Token
   */
  const setAuthineToken = (authineToken: string): void => {
    state.authineToken = authineToken;
    setStorage('authineToken', authineToken);
  };

  /**
   * 设置认证Token过期时间
   * @param authineTokenTime 认证Token过期时间
   */
  const setAuthineTokenExpiresIn = (authineTokenExpiresIn: number): void => {
    state.authineTokenExpiresIn = authineTokenExpiresIn;
    setStorage('authineTokenExpiresIn', authineTokenExpiresIn);
  };

  /**
   * 设置sessionKey
   * @param sessionKey 会话密钥
   */
  const setSessionKey = (sessionKey: string): void => {
    state.sessionKey = sessionKey;
    setStorage('sessionKey', sessionKey);
  };

  /**
   * 更新用户信息
   * @param user 用户信息
   */
  const getCurrentUserInfo = async (): Promise<void> => {
    if (state.hasLogin) {
      const { data, errcode } = await monkey.$api.user.getCurrentUserInfo(state.user?.OpenId || state.user?.openId);
      if (errcode == 200 && data) setUser(data.data);
    }
  };

  /**
   * 获取认证Token
   */
  const getAuthineToken = async (): Promise<void> => {
    const { access_token, expires_in, errcode } = await monkey.$api.auth.getAuthToken(18242078983);
    if (errcode == 0 && access_token) {
      setAuthineToken(access_token);
      //保存过期的时间点（当前时间 + 过期秒数）
      setAuthineTokenExpiresIn(Date.now() + expires_in * 1000);
    }
  };

  /**
   * 退出登录
   */
  const logout = (): void => {
    // 批量清除本地存储
    Object.keys(STORAGE_KEYS).forEach((key) => {
      removeStorage(key as StorageKey);
    });

    // 重置状态
    Object.assign(state, {
      user: null,
      token: null,
      hasLogin: false,
      sessionKey: null,
      authineToken: null,
    });
  };

  return {
    // 直接返回响应式状态，无需 computed
    ...toRefs(state),
    setUser,
    setToken,
    setSessionKey,
    setAuthineToken,
    setAuthineTokenExpiresIn,
    getCurrentUserInfo,
    getAuthineToken,
    logout,
  };
});
