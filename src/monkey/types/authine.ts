/**
 * 运行时查询列表参数
 */
export interface AuthineListParams {
  filters: any[];
  mobile: boolean;
  page: number;
  queryCode: string;
  schemaCode: string;
  size: number;
  queryVersion: number;
  queryCondition: any[];
  showTotal: boolean;
  customDisplayColumns: any[];
  customQueryFields: any[];
}

/**
 * 低代码表单查询详情参数
 */
export interface AuthineFormParams {
  sheetCode: string;
  objectId: string;
  schemaCode: string;
  isWorkFlow: boolean;
  _viewCode: string;
  return: number;
  pageInfo: string;
  relevanceInfo: string;
  isMobile: boolean;
}

export interface AuthineFormDeleteParams {
  ids: string[];
  schemaCode: string;
}

export interface AuthineFormSubmitParams {
  workflowCode: string;
  workItemId: string;
  workflowInstanceId: string;
  queryId: string;
  bizObject: any;
  agree: boolean;
  actionCode: string;
  latestSign: any;
  approval?: any; // 改为可选参数
  formType: string;
  replayToken: string;
}

export interface AuthineFormSubmitApproval {
  workItemId: string | null;
  workflowInstanceId: string;
  workflowTokenId: string | null;
  activityCode: string;
  activityName: string | null;
  commonSet: boolean;
  deleted: boolean;
  result: number;
}

export interface AuthineFormSubmitBizObject {
  id: string;
  data: any;
  schemaCode: string;
  sheetCode: string;
  workflowInstanceId: string | null;
}

export interface AuthineFormSubmitAllParams {
  activityCode: string;
  data: any;
  schemaCode: string;
  workflowCode: string;
  formType: string;
}

/**
 * 低代码列表查询条件
 */
export interface AuthineListQueryCondition {
  queryFilterType: string;
  propertyCode: string;
  propertyType: number;
  propertyValue: string;
}

export interface AuthineBizService {
  code: string;
  serviceCode: string;
  testInputParametersMap?: { [key: string]: string };
}

export interface AuthineListResponse<T> {
  content: T[];
}

export interface AuthineFormResponse<T> {
  bizObject: {
    data: T;
  };
}
