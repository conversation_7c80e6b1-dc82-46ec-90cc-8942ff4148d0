<template>
  <ly-layout :active="2">
    <!-- 顶部导航区域 -->
    <div class="bg-white/95 backdrop-blur-sm border-b border-gray-100">
      <div class="flex items-center justify-between px-4 py-2.5 gap-2">
        <!-- 搜索框 -->
        <div class="flex-1">
          <uni-search-bar padding="0" v-model="searchQuery" placeholder="搜索社区内容..." @confirm="handleSearch" @clear="clearSearch" @cancel="clearSearch" radius="20" cancel-button="none" :focus="false" />
        </div>

        <!-- 发布按钮 -->
        <div
          @click="handleCreatePost"
          class="flex items-center justify-center px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm rounded-full active:from-blue-600 active:to-blue-700 transition-all duration-300 shadow-lg shadow-blue-200/50"
        >
          <div class="i-mdi-plus text-lg"></div>
        </div>
        <!-- 消息按钮 -->
        <div
          @click="handleMessage"
          class="flex items-center justify-center px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-sm rounded-full active:from-blue-600 active:to-blue-700 transition-all duration-300 shadow-lg shadow-blue-200/50"
        >
          <div class="i-mdi-message-outline text-lg"></div>
        </div>
      </div>
    </div>

    <!-- 社区信息列表 -->
    <scroll-view
      class="px-4 absolute top-[70px] left-0 right-0 bottom-0 box-border"
      :scroll-y="true"
      refresher-enabled="true"
      refresher-default-style="none"
      :refresher-triggered="lazyload.triggered"
      @refresherrefresh="onRefresh"
      @refresherrestore="onRestore"
      @scrolltolower="onScrollTolower"
    >
      <!-- 骨架屏加载状态 骨架屏-->
      <ly-skeleton-screen :is-loading="lazyload.isLoading && list.length === 0" :count="3" />
      <!-- 加载中 -->
      <ly-loading v-if="lazyload.isLoading" />
      <!-- 社区信息卡片列表 -->
      <div v-else-if="list.length > 0" class="space-y-4">
        <ui-image-card v-for="post in list" :key="post.id" :post="post" @like="handleLike" @comment="handleComment" @share="handleShare" />
        <ly-line-bar height="110" />
      </div>
      <!-- 空状态 -->
      <ly-empty v-if="lazyload.isEmpty" description="暂无社区内容" :image="monkey.$url.cdn(monkey.$config.empty.noMessage)" />
      <ly-load-more v-if="list.length > 0" :status="lazyload.status" />
    </scroll-view>

    <!-- 评论弹窗 -->
    <van-popup v-model:show="showCommentModal" position="bottom" :style="{ height: '70vh' }" round closeable close-icon="close">
      <div class="h-full flex flex-col">
        <!-- 评论标题 -->
        <div class="flex items-center justify-between px-4 py-3 border-b border-gray-100">
          <text class="text-lg font-semibold text-gray-900">评论 {{ comments.length }}</text>
          <div @click="showCommentModal = false" class="i-mdi-close text-gray-400 text-xl"></div>
        </div>

        <!-- 评论列表 -->
        <div class="flex-1 overflow-y-auto px-4">
          <div v-if="comments.length === 0" class="flex flex-col items-center justify-center h-full">
            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <div class="i-mdi-comment-outline text-2xl text-gray-400"></div>
            </div>
            <p class="text-gray-500 text-sm">还没有评论，快来抢沙发吧~</p>
          </div>
          <div v-else class="divide-y divide-gray-100">
            <!-- <ly-comment-item v-for="comment in comments" :key="comment.id" :comment="comment" @like="handleCommentLike" @reply="handleCommentReply" /> -->
          </div>
        </div>

        <!-- 评论输入框 -->
        <div class="px-4 py-3 border-t border-gray-100 bg-white">
          <div class="flex space-x-3">
            <input
              v-model="commentText"
              type="text"
              placeholder="写下你的评论..."
              class="flex-1 px-4 py-3 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-300"
              @keyup.enter="submitComment"
            />
            <div @click="submitComment" :disabled="!commentText.trim()" class="px-6 py-3 bg-blue-500 text-white rounded-full active:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300">发送</div>
          </div>
        </div>
      </div>
    </van-popup>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import type { ImageTypes } from '@/monkey/types';

  // 响应式数据
  const list = ref<ImageTypes.ImagePost[]>([]); // 帖子列表
  const searchQuery = ref(''); // 搜索框
  const activeFilter = ref('all'); // 筛选标签
  const comments = ref<ImageTypes.ImageComment[]>([]); // 评论列表
  const showCommentModal = ref(false); // 评论弹窗
  const commentText = ref(''); // 评论输入框
  const currentPostId = ref(''); // 当前帖子ID

  // 懒加载配置
  const lazyload = reactive({
    page: 0,
    size: 4,
    isFlag: false, // 数据状态
    status: 'more',
    isEmpty: false, // 数据图片
    isLoading: false,
    pageCount: 0, // 总数
    triggered: false, // 监听刷新状态
    _freshing: false, // 刷新监听
  });

  // 显示创建帖子
  const handleCreatePost = () => {
    // 监听发布事件
    uni.$on('image:publish', (e) => {
      if (e.success) {
        init();
        getList();
      }
    });
    // 跳转至编辑页面
    monkey.$router.navigateTo('/modules/image/editor/index');
  };

  // 处理搜索
  const handleSearch = (e?: any) => {
    init();
    getList();
  };

  // 清除搜索
  const clearSearch = () => {
    searchQuery.value = '';
    init();
    getList();
  };

  /**
   * 获取帖子列表
   * @param callback 回调函数
   */
  const getList = async (callback) => {
    const queryCondition = [
      {
        ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'tpzt', 14, '[{"key":"shtg","value":"已通过"}]'),
      },
    ];
    if (searchQuery.value)
      queryCondition.push({
        ...monkey.$helper.param.getAuthineListQueryCondition('Like', 'fbnr', 1, searchQuery.value),
      });
    const params = monkey.$helper.param.getAuthineListParams({
      schemaCode: 'ltxxb',
      queryCondition: queryCondition,
    });
    const { errcode, data } = await monkey.$api.authine.getAuthineList(params);
    if (errcode === 0 && data?.content) {
      const newList = data.content.map((item) => ({ ...item.data })) as ImageTypes.ImagePost[];
      list.value = [...list.value, ...newList];
      lazyload.pageCount = data.totalElements; // 更新总页数
      lazyload.isEmpty = !list.value.length; // 更新数据状态
      callback && callback();
    }
  };

  /**
   * 初始化函数，用于重置状态和数据
   * @returns {void}
   */
  const init = () => {
    lazyload.isFlag = false; // 重置加载标志
    list.value = []; // 重置课程列表
    lazyload.page = 0; // 重置页码为第1页
  };

  // 下拉刷新的逻辑
  const onRefresh = () => {
    if (lazyload._freshing) return; // 如果正在刷新，则不执行
    lazyload._freshing = true; // 设置为正在刷新
    lazyload.isLoading = true;
    if (!lazyload.triggered) lazyload.triggered = true; // 初始时设置为已触发
    init(); // 初始化数据和状态
    setTimeout(() => {
      getList(() => {
        // 获取课程列表
        lazyload.triggered = false; // 重置触发状态
        lazyload._freshing = false; // 重置刷新状态
        lazyload.isLoading = false; // 重置加载状态
      });
    }, 600);
  };

  // 上拉加载更多的逻辑
  const onScrollTolower = () => {
    lazyload.isFlag = true; // 开启加载状态
    if (list.value.length >= lazyload.pageCount) return (lazyload.status = 'no-more');
    lazyload.status = 'loading'; // 设置加载状态为加载中
    lazyload.page++; // 加载下一页
    getList(); // 获取课程列表
  };

  // 用于恢复加载状态的函数 设置触发状态为恢复
  const onRestore = () => {
    lazyload.triggered = 'restore';
  };

  // 页面加载
  onLoad(async () => {
    getList();
  });

  // 处理点赞
  const handleLike = (postId: string) => {
    monkey.$helper.toast.error('暂未开放');
    // const post = posts.value.find((p) => p.id === postId);
    // if (post) {
    //   post.isLiked = !post.isLiked;
    //   post.likeCount += post.isLiked ? 1 : -1;
    // }
  };

  // 处理评论
  const handleComment = (postId: string) => {
    monkey.$helper.toast.error('暂未开放');
    // currentPostId.value = postId;
    // loadComments(postId);
    // showCommentModal.value = true;
  };

  // 处理分享
  const handleShare = (postId: string) => {
    uni.showShareMenu({
      success: () => {
        console.log('分享成功');
      },
    });
  };

  // 加载评论
  const loadComments = async (postId: string) => {
    // 模拟评论数据
    comments.value = [
      {
        id: 'comment_1',
        user: mockUser,
        content: '很棒的分享！',
        likeCount: 5,
        isLiked: false,
        createTime: new Date().toISOString(),
      },
      {
        id: 'comment_2',
        user: { ...mockUser, id: 'user_2', nc: '小红', tx: 'https://picsum.photos/100/100?random=2' },
        content: '确实很美，我也想去！',
        likeCount: 3,
        isLiked: true,
        createTime: new Date(Date.now() - 60000).toISOString(),
        replyToId: 'comment_1',
        replyToUser: '小明',
      },
    ];
  };

  // 提交评论
  const submitComment = () => {
    if (!commentText.value.trim()) return;

    const newComment: Comment = {
      id: `comment_${Date.now()}`,
      user: mockUser,
      content: commentText.value.trim(),
      likeCount: 0,
      isLiked: false,
      createTime: new Date().toISOString(),
    };

    comments.value.push(newComment);
    commentText.value = '';

    // 更新帖子评论数
    const post = posts.value.find((p) => p.id === currentPostId.value);
    if (post) {
      post.commentCount += 1;
    }
  };

  // 处理评论点赞
  const handleCommentLike = (commentId: string) => {
    const comment = comments.value.find((c) => c.id === commentId);
    if (comment) {
      comment.isLiked = !comment.isLiked;
      comment.likeCount += comment.isLiked ? 1 : -1;
    }
  };

  // 处理评论回复
  const handleCommentReply = (commentId: string, content: string) => {
    const parentComment = comments.value.find((c) => c.id === commentId);
    if (!parentComment) return;

    const replyComment: Comment = {
      id: `reply_${Date.now()}`,
      user: mockUser,
      content: content,
      likeCount: 0,
      isLiked: false,
      createTime: new Date().toISOString(),
      replyToId: commentId,
      replyToUser: parentComment.user.zsxm || parentComment.user.nc,
    };

    comments.value.push(replyComment);
  };
</script>